#!/usr/bin/env python3
"""
Plot NGOFS2 velocity vector field as quiver plot on cartopy map at t=12h
"""

import numpy as np
import xarray as xr
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from scipy.spatial import cKDTree
from datetime import datetime, timedelta

def download_ngofs2_data_12h():
    """Download NGOFS2 data for t=12h forecast"""
    base_url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/2025/06/12/"
    url = f"{base_url}ngofs2.t21z.20250612.fields.f012.nc"  # 12-hour forecast
    
    print(f"Loading NGOFS2 data for t=12h...")
    print(f"URL: {url}")
    
    try:
        ds = xr.open_dataset(url, engine='netcdf4')
        print("✅ Successfully loaded NGOFS2 dataset")
        return ds
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def create_vector_field_grid(ds, lon_min=-97.2, lon_max=-96.2, lat_min=27.8, lat_max=28.8, grid_res=0.02):
    """Create regular grid for vector field visualization"""
    
    # Create regular grid - coarser resolution for better vector visibility
    lons = np.arange(lon_min, lon_max + grid_res, grid_res)
    lats = np.arange(lat_min, lat_max + grid_res, grid_res)
    
    print(f"Creating {len(lons)}x{len(lats)} vector grid...")
    
    # Get coordinates from dataset
    lonc = ds['lonc'].values  # Element centers (velocities)
    latc = ds['latc'].values
    lon_nodes = ds['lon'].values  # Nodes (bathymetry)
    lat_nodes = ds['lat'].values
    h_nodes = ds['h'].values  # Depth at nodes
    
    # Get surface velocities (first time, surface layer)
    u_surf = ds['u'].values[0, 0, :]  # time=0, siglay=0 (surface)
    v_surf = ds['v'].values[0, 0, :]
    
    print(f"Velocity data shape: u={u_surf.shape}, v={v_surf.shape}")
    print(f"Velocity range: u=[{np.nanmin(u_surf):.3f}, {np.nanmax(u_surf):.3f}] m/s")
    print(f"Velocity range: v=[{np.nanmin(v_surf):.3f}, {np.nanmax(v_surf):.3f}] m/s")
    
    # Build KDTrees for interpolation
    vel_tree = cKDTree(np.column_stack([lonc, latc]))
    bathy_tree = cKDTree(np.column_stack([lon_nodes, lat_nodes]))
    
    # Create target grid
    lon_grid, lat_grid = np.meshgrid(lons, lats)
    target_points = np.column_stack([lon_grid.ravel(), lat_grid.ravel()])
    
    # Find nearest neighbors
    _, vel_indices = vel_tree.query(target_points, k=1)
    _, bathy_indices = bathy_tree.query(target_points, k=1)
    
    # Interpolate velocities to grid
    u_interp = u_surf[vel_indices].reshape(lon_grid.shape)
    v_interp = v_surf[vel_indices].reshape(lon_grid.shape)
    
    # Create land mask
    bathy_interp = h_nodes[bathy_indices].reshape(lon_grid.shape)
    land_mask = bathy_interp <= 2.0  # Land or very shallow water
    
    # Apply land mask - set velocities to NaN on land for better visualization
    u_interp[land_mask] = np.nan
    v_interp[land_mask] = np.nan
    
    print(f"Land mask: {np.sum(land_mask)} land points out of {land_mask.size} total")
    print(f"Valid velocity points: {np.sum(~np.isnan(u_interp))}")
    
    return lons, lats, lon_grid, lat_grid, u_interp, v_interp, bathy_interp, land_mask

def plot_vector_field():
    """Create cartopy map with NGOFS2 velocity vectors"""
    
    # Download data
    ds = download_ngofs2_data_12h()
    if ds is None:
        return
    
    # Create vector field grid
    lons, lats, lon_grid, lat_grid, u_interp, v_interp, bathy, land_mask = create_vector_field_grid(ds)
    
    # Try to import cartopy, fall back to simple matplotlib if not available
    try:
        import cartopy.crs as ccrs
        import cartopy.feature as cfeature
        from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
        use_cartopy = True
        print("✅ Using Cartopy for map projection")
    except ImportError:
        use_cartopy = False
        print("⚠️  Cartopy not available, using simple matplotlib")
    
    # Set up the plot
    if use_cartopy:
        fig = plt.figure(figsize=(16, 12))
        ax = plt.axes(projection=ccrs.PlateCarree())
        
        # Set map extent
        lon_margin = 0.1
        lat_margin = 0.1
        extent = [lons.min() - lon_margin, lons.max() + lon_margin,
                  lats.min() - lat_margin, lats.max() + lat_margin]
        ax.set_extent(extent, crs=ccrs.PlateCarree())
        
        # Add map features
        ax.add_feature(cfeature.LAND, color='lightgray', alpha=0.8)
        ax.add_feature(cfeature.OCEAN, color='lightblue', alpha=0.6)
        ax.add_feature(cfeature.COASTLINE, linewidth=1.5, color='black')
        ax.add_feature(cfeature.BORDERS, linewidth=1, color='gray')
        ax.add_feature(cfeature.STATES, linewidth=0.8, color='gray', alpha=0.7)
        
        # Add gridlines
        gl = ax.gridlines(draw_labels=True, dms=True, x_inline=False, y_inline=False,
                          linewidth=0.5, color='gray', alpha=0.7)
        gl.top_labels = False
        gl.right_labels = False
        gl.xformatter = LONGITUDE_FORMATTER
        gl.yformatter = LATITUDE_FORMATTER
        
        transform = ccrs.PlateCarree()
        
    else:
        fig, ax = plt.subplots(figsize=(16, 12))
        ax.set_xlim(lons.min() - 0.1, lons.max() + 0.1)
        ax.set_ylim(lats.min() - 0.1, lats.max() + 0.1)
        ax.set_facecolor('lightblue')
        
        # Add simple land representation
        land_contour = ax.contourf(lon_grid, lat_grid, land_mask.astype(int), 
                                   levels=[0.5, 1.5], colors=['lightgray'], alpha=0.8)
        
        ax.set_xlabel('Longitude (°W)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Latitude (°N)', fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3, linestyle='--')
        
        transform = None
    
    # Calculate velocity magnitude for color coding
    velocity_magnitude = np.sqrt(u_interp**2 + v_interp**2)
    
    # Subsample for better vector visibility (every nth point)
    skip = 3  # Show every 3rd vector
    lon_sub = lon_grid[::skip, ::skip]
    lat_sub = lat_grid[::skip, ::skip]
    u_sub = u_interp[::skip, ::skip]
    v_sub = v_interp[::skip, ::skip]
    mag_sub = velocity_magnitude[::skip, ::skip]
    
    # Create quiver plot
    if use_cartopy:
        quiver = ax.quiver(lon_sub, lat_sub, u_sub, v_sub, mag_sub,
                          scale=5, scale_units='xy', angles='xy',
                          cmap='viridis', alpha=0.8, width=0.003,
                          transform=transform)
    else:
        quiver = ax.quiver(lon_sub, lat_sub, u_sub, v_sub, mag_sub,
                          scale=5, scale_units='xy', angles='xy',
                          cmap='viridis', alpha=0.8, width=0.003)
    
    # Add colorbar
    cbar = plt.colorbar(quiver, ax=ax, shrink=0.8, pad=0.02)
    cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')
    
    # Add bathymetry contours
    if use_cartopy:
        contours = ax.contour(lon_grid, lat_grid, bathy, 
                             levels=[5, 10, 20, 50, 100], colors='gray', 
                             alpha=0.5, linewidths=0.8, transform=transform)
    else:
        contours = ax.contour(lon_grid, lat_grid, bathy, 
                             levels=[5, 10, 20, 50, 100], colors='gray', 
                             alpha=0.5, linewidths=0.8)
    
    ax.clabel(contours, inline=True, fontsize=8, fmt='%d m')
    
    # Add title
    plt.title('NGOFS2 Surface Velocity Field at t=12h\n' +
              'Northern Gulf of Mexico - June 12, 2025 21:00 UTC + 12h', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add information box
    info_text = """Vector Field Details:
• Time: t = 12 hours
• Layer: Surface (σ = 0)
• Resolution: 0.02° grid
• Data: NOAA NGOFS2
• Vectors: Every 3rd grid point
• Bathymetry: Gray contours (m)"""
    
    if use_cartopy:
        ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.9),
                fontsize=10, verticalalignment='bottom')
    else:
        ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.9),
                fontsize=10, verticalalignment='bottom')
    
    # Add statistics box
    valid_mask = ~np.isnan(velocity_magnitude)
    if np.any(valid_mask):
        max_vel = np.nanmax(velocity_magnitude)
        mean_vel = np.nanmean(velocity_magnitude)
        
        stats_text = f"""Velocity Statistics:
• Max: {max_vel:.3f} m/s
• Mean: {mean_vel:.3f} m/s
• Valid points: {np.sum(valid_mask)}
• Grid size: {lon_grid.shape}"""
        
        if use_cartopy:
            ax.text(0.98, 0.02, stats_text, transform=ax.transAxes,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcyan", alpha=0.9),
                    fontsize=10, verticalalignment='bottom', horizontalalignment='right')
        else:
            ax.text(0.98, 0.02, stats_text, transform=ax.transAxes,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcyan", alpha=0.9),
                    fontsize=10, verticalalignment='bottom', horizontalalignment='right')
    
    plt.tight_layout()
    
    # Save the plot
    filename = 'ngofs2_vector_field_t12h.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"✅ Vector field plot saved as '{filename}'")
    
    # Close dataset
    ds.close()
    
    print(f"\n✅ NGOFS2 velocity vector field plotted successfully!")
    print(f"✅ Time: t = 12 hours after forecast initialization")
    print(f"✅ Shows surface ocean currents in Northern Gulf of Mexico")

if __name__ == "__main__":
    plot_vector_field()
