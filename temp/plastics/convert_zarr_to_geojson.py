#!/usr/bin/env python3
"""
Convert PlasticParcels zarr output to GeoJSON format
"""

import numpy as np
import xarray as xr
import json
from datetime import datetime

def convert_zarr_to_geojson(zarr_path="ngofs2_plastic_trajectory.zarr", 
                           geojson_path="ngofs2_plastic_trajectory.geojson"):
    """Convert Parcels zarr output to GeoJSON format"""
    
    print("Loading trajectory data from zarr...")
    
    # Load the trajectory data
    ds = xr.open_dataset(zarr_path, engine='zarr')
    
    print(f"Dataset dimensions: {dict(ds.dims)}")
    print(f"Variables: {list(ds.data_vars)}")
    
    features = []
    
    # Process each particle trajectory
    for traj_id in ds.trajectory.values:
        print(f"Processing trajectory {traj_id}")
        
        # Select data for this trajectory
        traj_data = ds.sel(trajectory=traj_id)
        
        # Extract coordinates and time
        lons = traj_data.lon.values
        lats = traj_data.lat.values
        times = traj_data.time.values
        
        # Filter out NaN values
        valid_mask = ~(np.isnan(lons) | np.isnan(lats))
        
        if not np.any(valid_mask):
            print(f"  No valid data for trajectory {traj_id}")
            continue
            
        lons_valid = lons[valid_mask]
        lats_valid = lats[valid_mask]
        times_valid = times[valid_mask]
        
        print(f"  Found {len(lons_valid)} valid points")
        
        # Create coordinates for LineString
        coordinates = [[float(lon), float(lat)] for lon, lat in zip(lons_valid, lats_valid)]
        
        # Create time series
        time_series = []
        for i, (lon, lat, time_val) in enumerate(zip(lons_valid, lats_valid, times_valid)):
            # Convert time value to string
            try:
                if np.isnan(time_val):
                    continue
                # Convert seconds since epoch to datetime string
                time_dt = datetime.fromtimestamp(float(time_val))
                time_str = time_dt.isoformat()
            except (ValueError, TypeError, OSError):
                # Fallback: use index-based time
                time_str = f"2025-06-12T21:00:00+{i:02d}:00:00"

            time_series.append({
                'time': time_str,
                'coordinates': [float(lon), float(lat)]
            })
        
        if not time_series:
            print(f"  No valid time series for trajectory {traj_id}")
            continue
        
        # Create feature
        feature = {
            'type': 'Feature',
            'geometry': {
                'type': 'LineString',
                'coordinates': coordinates
            },
            'properties': {
                'particle_id': int(traj_id),
                'name': f'Plastic Particle {int(traj_id)}',
                'start_time': time_series[0]['time'],
                'end_time': time_series[-1]['time'],
                'duration_hours': len(time_series),
                'total_points': len(coordinates),
                'time_series': time_series
            }
        }
        
        # Add plastic properties if available
        if 'plastic_amount' in traj_data:
            plastic_amount = traj_data.plastic_amount.values
            if not np.all(np.isnan(plastic_amount)):
                feature['properties']['plastic_amount'] = float(plastic_amount[valid_mask][0])
        
        if 'plastic_density' in traj_data:
            plastic_density = traj_data.plastic_density.values
            if not np.all(np.isnan(plastic_density)):
                feature['properties']['plastic_density'] = float(plastic_density[valid_mask][0])
        
        if 'plastic_diameter' in traj_data:
            plastic_diameter = traj_data.plastic_diameter.values
            if not np.all(np.isnan(plastic_diameter)):
                feature['properties']['plastic_diameter'] = float(plastic_diameter[valid_mask][0])
        
        features.append(feature)
        print(f"  Added feature with {len(coordinates)} coordinates")
    
    # Create GeoJSON FeatureCollection
    geojson = {
        'type': 'FeatureCollection',
        'features': features,
        'properties': {
            'description': 'PlasticParcels trajectory simulation using NOAA NGOFS2 data',
            'simulation_type': 'plastic_particles',
            'total_particles': len(features),
            'data_source': 'NOAA NGOFS2',
            'model': 'PlasticParcels'
        }
    }
    
    # Save to file
    with open(geojson_path, 'w') as f:
        json.dump(geojson, f, indent=2)
    
    print(f"\nGeoJSON saved to {geojson_path}")
    print(f"Total features: {len(features)}")
    
    # Print summary
    if features:
        total_points = sum(len(f['geometry']['coordinates']) for f in features)
        print(f"Total trajectory points: {total_points}")
        print(f"Average points per particle: {total_points/len(features):.1f}")
        
        # Print first and last positions
        first_feature = features[0]
        first_coords = first_feature['geometry']['coordinates'][0]
        last_coords = first_feature['geometry']['coordinates'][-1]
        print(f"First particle start: {first_coords[1]:.6f}N, {abs(first_coords[0]):.6f}W")
        print(f"First particle end: {last_coords[1]:.6f}N, {abs(last_coords[0]):.6f}W")
    
    return geojson

def main():
    """Main function"""
    print("=== Converting PlasticParcels Output to GeoJSON ===")
    
    geojson = convert_zarr_to_geojson()
    
    print("\nConversion complete!")

if __name__ == "__main__":
    main()
