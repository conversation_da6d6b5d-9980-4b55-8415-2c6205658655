#!/usr/bin/env python3
"""
Debug NGOFS2 domain and check for land/water boundaries
"""

import xarray as xr
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

def check_ngofs2_domain():
    """Check the NGOFS2 domain and identify land/water boundaries"""
    
    url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/2025/06/12/ngofs2.t21z.20250612.fields.f000.nc"
    
    print("Loading NGOFS2 data to check domain...")
    ds = xr.open_dataset(url, engine='netcdf4')
    
    # Get coordinates
    lonc = ds['lonc'].values  # Element centers
    latc = ds['latc'].values
    lon_nodes = ds['lon'].values  # Node coordinates
    lat_nodes = ds['lat'].values
    
    # Get velocities
    u = ds['u'].values[0, 0, :]  # Surface layer
    v = ds['v'].values[0, 0, :]
    
    # Get bathymetry (depth)
    h = ds['h'].values  # Depth at nodes
    
    print(f"Element centers: {len(lonc)} points")
    print(f"Nodes: {len(lon_nodes)} points")
    print(f"Longitude range (elements): {lonc.min():.3f} to {lonc.max():.3f}")
    print(f"Latitude range (elements): {latc.min():.3f} to {latc.max():.3f}")
    print(f"Longitude range (nodes): {lon_nodes.min():.3f} to {lon_nodes.max():.3f}")
    print(f"Latitude range (nodes): {lat_nodes.min():.3f} to {lat_nodes.max():.3f}")
    
    # Check our initial position
    initial_lat = 28.3785805
    initial_lon = -96.7531298
    
    print(f"\nInitial position: {initial_lat:.6f}N, {abs(initial_lon):.6f}W")
    
    # Check if initial position is within domain
    in_lon_range = (lonc.min() <= initial_lon <= lonc.max())
    in_lat_range = (latc.min() <= initial_lat <= latc.max())
    
    print(f"Initial position in longitude range: {in_lon_range}")
    print(f"Initial position in latitude range: {in_lat_range}")
    
    # Find nearest elements to initial position
    distances = np.sqrt((lonc - initial_lon)**2 + (latc - initial_lat)**2)
    nearest_indices = np.argsort(distances)[:10]
    
    print(f"\nNearest 10 elements to initial position:")
    for i, idx in enumerate(nearest_indices):
        dist_km = distances[idx] * 111.32
        print(f"  {i+1}: lon={lonc[idx]:.6f}, lat={latc[idx]:.6f}, "
              f"u={u[idx]:.4f}, v={v[idx]:.4f}, dist={dist_km:.2f}km")
    
    # Check bathymetry near initial position
    node_distances = np.sqrt((lon_nodes - initial_lon)**2 + (lat_nodes - initial_lat)**2)
    nearest_node_idx = np.argmin(node_distances)
    nearest_depth = h[nearest_node_idx]
    
    print(f"\nNearest node depth: {nearest_depth:.2f} m")
    
    # Check for land (negative depth or very shallow)
    land_mask = h <= 0  # Negative or zero depth indicates land
    shallow_mask = (h > 0) & (h < 1)  # Very shallow water
    
    print(f"Land nodes (depth <= 0): {np.sum(land_mask)}")
    print(f"Very shallow nodes (0 < depth < 1m): {np.sum(shallow_mask)}")
    print(f"Water nodes (depth >= 1m): {np.sum(h >= 1)}")
    
    # Create a plot to visualize the domain
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Element centers with velocities
    valid_vel = ~(np.isnan(u) | np.isnan(v))
    ax1.scatter(lonc[valid_vel], latc[valid_vel], c=np.sqrt(u[valid_vel]**2 + v[valid_vel]**2), 
               s=1, cmap='viridis', alpha=0.5)
    ax1.plot(initial_lon, initial_lat, 'ro', markersize=10, label='Initial Position')
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.set_title('NGOFS2 Element Centers with Velocity Magnitude')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Bathymetry
    water_nodes = h > 0
    ax2.scatter(lon_nodes[water_nodes], lat_nodes[water_nodes], c=h[water_nodes], 
               s=1, cmap='Blues', alpha=0.5)
    ax2.scatter(lon_nodes[land_mask], lat_nodes[land_mask], c='brown', s=1, alpha=0.5, label='Land')
    ax2.plot(initial_lon, initial_lat, 'ro', markersize=10, label='Initial Position')
    ax2.set_xlabel('Longitude')
    ax2.set_ylabel('Latitude')
    ax2.set_title('NGOFS2 Bathymetry (Blue=Water, Brown=Land)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Zoom around initial position
    zoom_range = 0.1  # degrees
    zoom_mask = ((lonc >= initial_lon - zoom_range) & (lonc <= initial_lon + zoom_range) &
                 (latc >= initial_lat - zoom_range) & (latc <= initial_lat + zoom_range))
    
    if np.any(zoom_mask):
        ax3.scatter(lonc[zoom_mask], latc[zoom_mask], c=np.sqrt(u[zoom_mask]**2 + v[zoom_mask]**2), 
                   s=10, cmap='viridis')
        ax3.plot(initial_lon, initial_lat, 'ro', markersize=15, label='Initial Position')
        ax3.set_xlabel('Longitude')
        ax3.set_ylabel('Latitude')
        ax3.set_title('Zoom: Velocity Field Near Initial Position')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # Plot 4: Check trajectory direction
    # Sample some velocities in the area
    area_mask = ((lonc >= initial_lon - 0.05) & (lonc <= initial_lon + 0.05) &
                 (latc >= initial_lat - 0.05) & (latc <= initial_lat + 0.05))
    
    if np.any(area_mask):
        u_area = u[area_mask]
        v_area = v[area_mask]
        lonc_area = lonc[area_mask]
        latc_area = latc[area_mask]
        
        # Plot velocity vectors
        ax4.quiver(lonc_area, latc_area, u_area, v_area, scale=5, alpha=0.7)
        ax4.plot(initial_lon, initial_lat, 'ro', markersize=15, label='Initial Position')
        ax4.set_xlabel('Longitude')
        ax4.set_ylabel('Latitude')
        ax4.set_title('Velocity Vectors Near Initial Position')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # Print velocity statistics
        print(f"\nVelocity statistics near initial position:")
        print(f"U (eastward): mean={np.nanmean(u_area):.4f}, std={np.nanstd(u_area):.4f}")
        print(f"V (northward): mean={np.nanmean(v_area):.4f}, std={np.nanstd(v_area):.4f}")
        print(f"Speed: mean={np.nanmean(np.sqrt(u_area**2 + v_area**2)):.4f}")
    
    plt.tight_layout()
    plt.savefig('ngofs2_domain_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\nDomain analysis plot saved as ngofs2_domain_analysis.png")
    
    ds.close()
    
    # Check if the problem is with our grid interpolation
    print(f"\n=== PROBLEM DIAGNOSIS ===")
    
    if np.nanmean(v[area_mask]) > 0:
        print("⚠️  ISSUE: Velocities show strong NORTHWARD component")
        print("   This would push particles toward land!")
    
    if initial_lat > 28.5:
        print("⚠️  ISSUE: Initial position may be too close to shore")
    
    print("\nRecommendations:")
    print("1. Move initial position further offshore (lower latitude)")
    print("2. Add land mask to prevent particles from entering land areas")
    print("3. Check if NGOFS2 velocities are realistic for this location")
    print("4. Implement proper boundary conditions")

if __name__ == "__main__":
    check_ngofs2_domain()
