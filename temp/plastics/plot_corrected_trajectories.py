#!/usr/bin/env python3
"""
<PERSON><PERSON> corrected NGOFS2 trajectories on a cartopy map
"""

import json
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
import matplotlib.patches as mpatches

def load_trajectories(filename="corrected_ngofs2_trajectory.geojson"):
    """Load trajectory data from GeoJSON file"""
    with open(filename, 'r') as f:
        data = json.load(f)
    
    trajectories = []
    for feature in data['features']:
        coords = feature['geometry']['coordinates']
        lons = [coord[0] for coord in coords]
        lats = [coord[1] for coord in coords]
        
        trajectory = {
            'particle_id': feature['properties']['particle_id'],
            'name': feature['properties']['name'],
            'lons': np.array(lons),
            'lats': np.array(lats),
            'start_time': feature['properties']['start_time'],
            'end_time': feature['properties']['end_time'],
            'duration_hours': feature['properties']['duration_hours']
        }
        trajectories.append(trajectory)
    
    return trajectories

def plot_trajectories():
    """Create cartopy map with corrected trajectories"""
    
    # Load trajectory data
    trajectories = load_trajectories()
    
    # Set up the map
    fig = plt.figure(figsize=(14, 10))
    
    # Use PlateCarree projection for the Gulf of Mexico
    ax = plt.axes(projection=ccrs.PlateCarree())
    
    # Set map extent (Gulf of Mexico region)
    # Extend slightly beyond trajectory bounds for context
    all_lons = np.concatenate([traj['lons'] for traj in trajectories])
    all_lats = np.concatenate([traj['lats'] for traj in trajectories])
    
    lon_margin = 0.3
    lat_margin = 0.2
    extent = [all_lons.min() - lon_margin, all_lons.max() + lon_margin,
              all_lats.min() - lat_margin, all_lats.max() + lat_margin]
    ax.set_extent(extent, crs=ccrs.PlateCarree())
    
    # Add map features
    ax.add_feature(cfeature.LAND, color='lightgray', alpha=0.8)
    ax.add_feature(cfeature.OCEAN, color='lightblue', alpha=0.6)
    ax.add_feature(cfeature.COASTLINE, linewidth=1.5, color='black')
    ax.add_feature(cfeature.BORDERS, linewidth=1, color='gray')
    ax.add_feature(cfeature.STATES, linewidth=0.8, color='gray', alpha=0.7)
    
    # Add bathymetry/depth contours if available
    ax.add_feature(cfeature.LAKES, color='lightblue', alpha=0.8)
    ax.add_feature(cfeature.RIVERS, color='blue', alpha=0.6)
    
    # Define colors for different particles
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
    
    # Plot trajectories
    legend_elements = []
    
    for i, traj in enumerate(trajectories):
        color = colors[i % len(colors)]
        
        # Plot trajectory line
        ax.plot(traj['lons'], traj['lats'], 
                color=color, linewidth=2.5, alpha=0.8,
                transform=ccrs.PlateCarree(),
                label=f"Particle {traj['particle_id']}")
        
        # Mark start position
        ax.plot(traj['lons'][0], traj['lats'][0], 
                marker='o', markersize=10, color=color,
                markeredgecolor='black', markeredgewidth=1.5,
                transform=ccrs.PlateCarree())
        
        # Mark end position
        ax.plot(traj['lons'][-1], traj['lats'][-1], 
                marker='s', markersize=8, color=color,
                markeredgecolor='black', markeredgewidth=1.5,
                transform=ccrs.PlateCarree())
        
        # Add trajectory direction arrows
        n_arrows = 5
        arrow_indices = np.linspace(1, len(traj['lons'])-2, n_arrows, dtype=int)
        
        for idx in arrow_indices:
            dx = traj['lons'][idx+1] - traj['lons'][idx-1]
            dy = traj['lats'][idx+1] - traj['lats'][idx-1]
            
            ax.annotate('', xy=(traj['lons'][idx+1], traj['lats'][idx+1]),
                       xytext=(traj['lons'][idx], traj['lats'][idx]),
                       arrowprops=dict(arrowstyle='->', color=color, lw=1.5),
                       transform=ccrs.PlateCarree())
        
        # Create legend element
        legend_elements.append(mpatches.Patch(color=color, 
                                            label=f"Particle {traj['particle_id']}"))
    
    # Add gridlines
    gl = ax.gridlines(draw_labels=True, dms=True, x_inline=False, y_inline=False,
                      linewidth=0.5, color='gray', alpha=0.7)
    gl.top_labels = False
    gl.right_labels = False
    gl.xformatter = LONGITUDE_FORMATTER
    gl.yformatter = LATITUDE_FORMATTER
    gl.xlabel_style = {'size': 10}
    gl.ylabel_style = {'size': 10}
    
    # Add title and labels
    plt.title('Corrected NGOFS2 Plastic Particle Trajectories\n' +
              'Northern Gulf of Mexico - 36 Hour Simulation', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add legend
    legend1 = ax.legend(handles=legend_elements, loc='upper left', 
                       bbox_to_anchor=(0.02, 0.98), fontsize=10)
    
    # Add symbol legend
    start_marker = mpatches.Patch(color='black', label='Start Position (○)')
    end_marker = mpatches.Patch(color='black', label='End Position (□)')
    arrow_marker = mpatches.Patch(color='black', label='Direction (→)')
    
    legend2 = ax.legend(handles=[start_marker, end_marker, arrow_marker], 
                       loc='upper right', bbox_to_anchor=(0.98, 0.98), fontsize=10)
    ax.add_artist(legend1)  # Keep both legends
    
    # Add text box with simulation info
    info_text = f"""Simulation Details:
• Duration: {trajectories[0]['duration_hours']} hours
• Particles: {len(trajectories)}
• Start: {trajectories[0]['start_time'][:10]}
• Data: NOAA NGOFS2
• Corrections: Land masking applied"""
    
    ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.8),
            fontsize=9, verticalalignment='bottom')
    
    # Add comparison note
    comparison_text = """✅ CORRECTED SIMULATION
• Particles stay in water
• Realistic ocean movement
• No land boundary violations"""
    
    ax.text(0.98, 0.02, comparison_text, transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8),
            fontsize=9, verticalalignment='bottom', horizontalalignment='right')
    
    plt.tight_layout()
    
    # Save the plot
    plt.savefig('corrected_ngofs2_trajectories_map.png', dpi=300, bbox_inches='tight')
    print("Map saved as 'corrected_ngofs2_trajectories_map.png'")
    
    # Display trajectory statistics
    print("\n=== Trajectory Statistics ===")
    for traj in trajectories:
        start_pos = f"{traj['lats'][0]:.3f}°N, {abs(traj['lons'][0]):.3f}°W"
        end_pos = f"{traj['lats'][-1]:.3f}°N, {abs(traj['lons'][-1]):.3f}°W"
        
        # Calculate total distance
        distances = []
        for i in range(1, len(traj['lons'])):
            dlat = traj['lats'][i] - traj['lats'][i-1]
            dlon = traj['lons'][i] - traj['lons'][i-1]
            # Approximate distance in km
            dist_km = np.sqrt((dlat * 111.32)**2 + (dlon * 111.32 * np.cos(np.radians(traj['lats'][i])))**2)
            distances.append(dist_km)
        
        total_distance = sum(distances)
        
        print(f"\nParticle {traj['particle_id']}:")
        print(f"  Start: {start_pos}")
        print(f"  End:   {end_pos}")
        print(f"  Total distance: {total_distance:.1f} km")
        print(f"  Duration: {traj['duration_hours']} hours")
        print(f"  Avg speed: {total_distance/traj['duration_hours']:.2f} km/h")
    
    # plt.show()  # Commented out for headless environment

if __name__ == "__main__":
    plot_trajectories()
