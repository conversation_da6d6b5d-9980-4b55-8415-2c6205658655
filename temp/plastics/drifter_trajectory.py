#!/usr/bin/env python3
"""
Calculate 3-day drifter trajectory using NOAA NGOFS2 ocean current data
Initial position: 28.3785805N, 96.7531298W
Output: GeoJSON format with hourly positions
"""

import xarray as xr
import numpy as np
import json
from datetime import datetime, timedelta
from scipy.spatial import cKDTree
import warnings
warnings.filterwarnings('ignore')

class DrifterTrajectory:
    def __init__(self, initial_lat, initial_lon):
        self.initial_lat = initial_lat
        self.initial_lon = initial_lon
        self.trajectory = []
        
    def download_data_sequence(self, base_date="2025/06/12", base_hour=21, num_hours=48):
        """Download sequential forecast files for 2-3 days (flexible)"""
        self.datasets = []
        self.times = []
        
        base_url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/"
        
        # For simplicity, we'll use multiple forecast hours from the same run
        # In practice, you'd want to use multiple forecast runs
        for hour_offset in range(0, min(num_hours, 120), 6):  # Every 6 hours, max 120h forecast
            forecast_hour = f"f{hour_offset:03d}"
            url = f"{base_url}{base_date}/ngofs2.t{base_hour:02d}z.20250612.fields.{forecast_hour}.nc"
            
            try:
                print(f"Loading forecast hour {hour_offset}...")
                ds = xr.open_dataset(url, engine='netcdf4')
                self.datasets.append(ds)
                
                # Calculate actual time for this forecast
                base_time = datetime(2025, 6, 12, base_hour)
                forecast_time = base_time + timedelta(hours=hour_offset)
                self.times.append(forecast_time)
                
            except Exception as e:
                print(f"Failed to load {url}: {e}")
                break
                
        print(f"Loaded {len(self.datasets)} datasets")
        
    def find_containing_triangle(self, lon, lat, lonc, latc, nv):
        """Find triangle containing the given point using barycentric coordinates"""
        # Build KDTree for fast nearest neighbor search
        points = np.column_stack([lonc, latc])
        tree = cKDTree(points)
        
        # Find nearest elements to search
        _, nearest_indices = tree.query([lon, lat], k=50)
        
        for elem_idx in nearest_indices:
            # Get vertices of this triangle
            vertices = nv[:, elem_idx] - 1  # Convert to 0-based indexing
            
            # Get coordinates of triangle vertices
            if hasattr(self.datasets[0], 'lon'):
                lon_vertices = self.datasets[0]['lon'].values[vertices]
                lat_vertices = self.datasets[0]['lat'].values[vertices]
            else:
                # Use element center coordinates as approximation
                continue
                
            # Check if point is inside triangle using barycentric coordinates
            if self.point_in_triangle(lon, lat, lon_vertices, lat_vertices):
                return elem_idx, vertices
                
        return None, None
    
    def point_in_triangle(self, px, py, x1, y1, x2, y2, x3, y3):
        """Check if point (px, py) is inside triangle using barycentric coordinates"""
        denom = (y2 - y3) * (x1 - x3) + (x3 - x2) * (y1 - y3)
        if abs(denom) < 1e-10:
            return False
            
        a = ((y2 - y3) * (px - x3) + (x3 - x2) * (py - y3)) / denom
        b = ((y3 - y1) * (px - x3) + (x1 - x3) * (py - y3)) / denom
        c = 1 - a - b
        
        return a >= 0 and b >= 0 and c >= 0
    
    def point_in_triangle(self, px, py, vertices_lon, vertices_lat):
        """Simplified point in triangle test"""
        x1, x2, x3 = vertices_lon
        y1, y2, y3 = vertices_lat
        
        denom = (y2 - y3) * (x1 - x3) + (x3 - x2) * (y1 - y3)
        if abs(denom) < 1e-10:
            return False
            
        a = ((y2 - y3) * (px - x3) + (x3 - x2) * (py - y3)) / denom
        b = ((y3 - y1) * (px - x3) + (x1 - x3) * (py - y3)) / denom
        c = 1 - a - b
        
        return a >= 0 and b >= 0 and c >= 0
    
    def interpolate_velocity(self, lon, lat, ds):
        """Interpolate velocity at given position using nearest neighbor for now"""
        # Simple nearest neighbor interpolation
        lonc = ds['lonc'].values
        latc = ds['latc'].values
        
        # Find nearest element
        distances = np.sqrt((lonc - lon)**2 + (latc - lat)**2)
        nearest_elem = np.argmin(distances)
        
        # Get surface velocities (first sigma layer)
        u_surf = ds['u'].values[0, 0, nearest_elem]  # time=0, siglay=0 (surface)
        v_surf = ds['v'].values[0, 0, nearest_elem]
        
        return u_surf, v_surf
    
    def rk4_step(self, lon, lat, dt, ds):
        """4th-order Runge-Kutta integration step"""
        # Convert dt from hours to seconds
        dt_sec = dt * 3600
        
        # RK4 coefficients
        k1_u, k1_v = self.interpolate_velocity(lon, lat, ds)
        
        k2_u, k2_v = self.interpolate_velocity(
            lon + 0.5 * k1_u * dt_sec / 111320,  # Approximate degree conversion
            lat + 0.5 * k1_v * dt_sec / 111320, ds)
        
        k3_u, k3_v = self.interpolate_velocity(
            lon + 0.5 * k2_u * dt_sec / 111320,
            lat + 0.5 * k2_v * dt_sec / 111320, ds)
        
        k4_u, k4_v = self.interpolate_velocity(
            lon + k3_u * dt_sec / 111320,
            lat + k3_v * dt_sec / 111320, ds)
        
        # Final velocity
        u_avg = (k1_u + 2*k2_u + 2*k3_u + k4_u) / 6
        v_avg = (k1_v + 2*k2_v + 2*k3_v + k4_v) / 6
        
        # Update position (convert m/s to degrees)
        # Approximate conversion: 1 degree ≈ 111320 m
        new_lon = lon + u_avg * dt_sec / (111320 * np.cos(np.radians(lat)))
        new_lat = lat + v_avg * dt_sec / 111320
        
        return new_lon, new_lat
    
    def calculate_trajectory(self):
        """Calculate 2-3 day trajectory with hourly output"""
        if not self.datasets:
            print("No datasets loaded!")
            return
        
        # Initialize position
        current_lon = self.initial_lon
        current_lat = self.initial_lat
        
        # Add initial position
        self.trajectory.append({
            'time': self.times[0].isoformat(),
            'longitude': current_lon,
            'latitude': current_lat
        })
        
        # Time step (1 hour)
        dt = 1.0  # hours
        
        # Calculate trajectory - interpolate between available time steps
        total_hours = min(48, len(self.datasets) * 6)  # Max 48 hours or available data

        for hour in range(1, total_hours + 1):
            # Find which dataset to use (interpolate between datasets if needed)
            dataset_index = min(hour // 6, len(self.datasets) - 1)
            ds = self.datasets[dataset_index]

            try:
                # Take one hour step
                new_lon, new_lat = self.rk4_step(current_lon, current_lat, dt, ds)

                # Update position
                current_lon = new_lon
                current_lat = new_lat

                # Add to trajectory
                trajectory_time = self.times[0] + timedelta(hours=hour)
                self.trajectory.append({
                    'time': trajectory_time.isoformat(),
                    'longitude': current_lon,
                    'latitude': current_lat
                })

                if hour % 6 == 0:  # Print every 6 hours
                    print(f"Hour {hour}: {current_lat:.6f}N, {current_lon:.6f}W")

            except Exception as e:
                print(f"Error at hour {hour}: {e}")
                break
    
    def export_geojson(self, filename="drifter_trajectory.geojson"):
        """Export trajectory as GeoJSON"""
        # Create coordinates array for LineString
        coordinates = [[point['longitude'], point['latitude']] for point in self.trajectory]
        
        # Create properties with time series
        properties = {
            'name': 'Drifter Trajectory',
            'start_time': self.trajectory[0]['time'],
            'end_time': self.trajectory[-1]['time'],
            'duration_hours': len(self.trajectory) - 1,
            'initial_position': [self.initial_lon, self.initial_lat],
            'time_series': [{'time': point['time'], 
                           'coordinates': [point['longitude'], point['latitude']]} 
                          for point in self.trajectory]
        }
        
        # Create GeoJSON
        geojson = {
            'type': 'FeatureCollection',
            'features': [
                {
                    'type': 'Feature',
                    'geometry': {
                        'type': 'LineString',
                        'coordinates': coordinates
                    },
                    'properties': properties
                }
            ]
        }
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(geojson, f, indent=2)
        
        print(f"Trajectory saved to {filename}")
        return geojson

def main():
    # Initial drifter position
    initial_lat = 28.3785805  # N
    initial_lon = -96.7531298  # W (note: negative for western longitude)
    
    print(f"Calculating drifter trajectory from {initial_lat}N, {initial_lon}W")
    
    # Create trajectory calculator
    drifter = DrifterTrajectory(initial_lat, initial_lon)
    
    # Download data
    drifter.download_data_sequence()
    
    # Calculate trajectory
    drifter.calculate_trajectory()
    
    # Export to GeoJSON
    geojson = drifter.export_geojson()
    
    print(f"Trajectory calculated with {len(drifter.trajectory)} points")
    
    return geojson

if __name__ == "__main__":
    main()
