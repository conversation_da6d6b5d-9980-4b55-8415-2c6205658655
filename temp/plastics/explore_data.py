#!/usr/bin/env python3
"""
Explore NOAA NGOFS2 ocean current data structure
"""
import xarray as xr
import numpy as np

# Use the working URL provided by user
url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/2025/06/12/ngofs2.t21z.20250612.fields.f021.nc"

print("Opening dataset...")
try:
    ds = xr.open_dataset(url, engine='netcdf4')
    print(f"Success with: {url}")
except Exception as e:
    print(f"Failed: {e}")
    exit(1)

print("\nDataset info:")
print(ds)

print("\nCoordinates:")
for coord in ds.coords:
    print(f"{coord}: {ds[coord].shape} - {ds[coord].dims}")

print("\nData variables:")
for var in ds.data_vars:
    print(f"{var}: {ds[var].shape} - {ds[var].dims}")

print("\nTime coordinate details:")
print(ds['time'])

print("\nLat/Lon coordinate details:")
if 'lat' in ds.coords:
    print(f"Latitude range: {ds['lat'].min().item():.3f} to {ds['lat'].max().item():.3f}")
if 'lon' in ds.coords:
    print(f"Longitude range: {ds['lon'].min().item():.3f} to {ds['lon'].max().item():.3f}")

print("\nU velocity details:")
print(ds['u_velocity'])

print("\nV velocity details:")
print(ds['v_velocity'])
