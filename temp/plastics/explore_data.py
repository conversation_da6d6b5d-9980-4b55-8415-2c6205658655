#!/usr/bin/env python3
"""
Explore NOAA NGOFS2 ocean current data structure
"""
import xarray as xr
import numpy as np

# URL for NGOFS2 data
url = ("https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/"
       "OFS/ngofs2/2025/06/01/ngofs2_20250601_00.nc")

print("Opening dataset...")
# Use netcdf4 engine for OPeNDAP URLs
ds = xr.open_dataset(url, engine='netcdf4')

print("\nDataset info:")
print(ds)

print("\nCoordinates:")
for coord in ds.coords:
    print(f"{coord}: {ds[coord].shape} - {ds[coord].dims}")

print("\nData variables:")
for var in ds.data_vars:
    print(f"{var}: {ds[var].shape} - {ds[var].dims}")

print("\nTime coordinate details:")
print(ds['time'])

print("\nLat/Lon coordinate details:")
if 'lat' in ds.coords:
    print(f"Latitude range: {ds['lat'].min().item():.3f} to {ds['lat'].max().item():.3f}")
if 'lon' in ds.coords:
    print(f"Longitude range: {ds['lon'].min().item():.3f} to {ds['lon'].max().item():.3f}")

print("\nU velocity details:")
print(ds['u_velocity'])

print("\nV velocity details:")
print(ds['v_velocity'])
