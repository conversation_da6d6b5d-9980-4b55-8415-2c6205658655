#!/usr/bin/env python3
"""
Explore NOAA NGOFS2 ocean current data structure
"""
import xarray as xr
import numpy as np

# Try different URLs for NGOFS2 data - try recent dates and different servers
urls_to_try = [
    # Try NOMADS server
    "https://nomads.ncep.noaa.gov/dods/nos/ngofs2/ngofs2_20241214/ngofs2_20241214_t00z",
    "https://nomads.ncep.noaa.gov/dods/nos/ngofs2/ngofs2_20241213/ngofs2_20241213_t00z",
    # Try original server with different dates
    "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/OFS/ngofs2/2024/06/15/ngofs2_20240615_00.nc",
    "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/OFS/ngofs2/2024/06/14/ngofs2_20240614_00.nc",
]

print("Trying to open dataset...")
# Try each URL until one works
ds = None
for url in urls_to_try:
    try:
        print(f"Trying: {url}")
        ds = xr.open_dataset(url, engine='netcdf4')
        print(f"Success with: {url}")
        break
    except Exception as e:
        print(f"Failed: {e}")
        continue

if ds is None:
    print("Could not open any dataset")
    exit(1)

print("\nDataset info:")
print(ds)

print("\nCoordinates:")
for coord in ds.coords:
    print(f"{coord}: {ds[coord].shape} - {ds[coord].dims}")

print("\nData variables:")
for var in ds.data_vars:
    print(f"{var}: {ds[var].shape} - {ds[var].dims}")

print("\nTime coordinate details:")
print(ds['time'])

print("\nLat/Lon coordinate details:")
if 'lat' in ds.coords:
    print(f"Latitude range: {ds['lat'].min().item():.3f} to {ds['lat'].max().item():.3f}")
if 'lon' in ds.coords:
    print(f"Longitude range: {ds['lon'].min().item():.3f} to {ds['lon'].max().item():.3f}")

print("\nU velocity details:")
print(ds['u_velocity'])

print("\nV velocity details:")
print(ds['v_velocity'])
