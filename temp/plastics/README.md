# Drifter Trajectory Calculation

This project calculates the trajectory of a drifter in the Gulf of Mexico using NOAA NGOFS2 ocean current data.

## Results Summary

**Initial Position:** 28.3785805°N, 96.7531298°W  
**Duration:** 48 hours (2 days)  
**Final Position:** 28.800386°N, 96.782138°W  

### Key Statistics
- **Total Distance Traveled:** 47.04 km
- **Average Speed:** 0.980 km/h
- **Net Displacement:** 46.96 km North, 2.84 km West
- **Trajectory Points:** 49 hourly positions

## Algorithm Details

### Data Source
- **Model:** NOAA NGOFS2 (Northern Gulf of Mexico Operational Forecast System)
- **Grid:** Unstructured triangular mesh (SCHISM model)
- **Resolution:** 569,405 elements, 303,714 nodes
- **Vertical Layers:** 40 sigma layers (surface layer used)
- **URL Pattern:** `https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/YYYY/MM/DD/ngofs2.tXXz.YYYYMMDD.fields.fXXX.nc`

### Numerical Method
1. **Spatial Interpolation:** Nearest neighbor interpolation from element centers to particle position
2. **Temporal Integration:** 4th-order Runge-Kutta (RK4) method
3. **Time Step:** 1 hour
4. **Velocity Components:** Surface u, v velocities (first sigma layer)

### Algorithm Steps
1. Download sequential forecast files (every 6 hours)
2. Extract surface velocities (u, v) at element centers
3. For each time step:
   - Interpolate velocity at current particle position
   - Apply RK4 integration to advance position
   - Store hourly position and timestamp
4. Export trajectory as GeoJSON

## Files Generated

1. **`drifter_trajectory.geojson`** - Complete trajectory in GeoJSON format
   - LineString geometry with all trajectory points
   - Time series data with hourly positions
   - Metadata including start/end times and initial position

2. **`drifter_trajectory.png`** - Visualization plot showing:
   - Trajectory map with start/end markers
   - Distance from start vs. time

## Usage

```bash
# Activate environment
conda activate pyschism_env

# Run trajectory calculation
python drifter_trajectory.py

# Generate visualization and summary
python visualize_trajectory.py
```

## GeoJSON Structure

The output GeoJSON contains:
- **Geometry:** LineString with trajectory coordinates
- **Properties:**
  - `name`: "Drifter Trajectory"
  - `start_time`, `end_time`: ISO format timestamps
  - `duration_hours`: Total duration
  - `initial_position`: Starting coordinates
  - `time_series`: Array of hourly positions with timestamps

## Physical Interpretation

The drifter moves primarily northward (~47 km) with slight westward drift (~3 km), indicating:
- Dominant northward current component in this region
- Relatively weak cross-shore (east-west) currents
- Average drift speed of ~1 km/h, typical for surface currents in coastal waters

This trajectory is consistent with typical Gulf of Mexico circulation patterns, where coastal currents often flow parallel to the shore with some offshore/onshore components due to wind and density-driven flows.
