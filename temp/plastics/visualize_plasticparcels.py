#!/usr/bin/env python3
"""
Visualize PlasticParcels trajectory results
"""

import json
import numpy as np
from datetime import datetime
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt

def load_and_visualize_plasticparcels(filename="ngofs2_plastic_trajectory.geojson"):
    """Load and visualize PlasticParcels trajectory results"""
    
    # Load GeoJSON
    with open(filename, 'r') as f:
        geojson = json.load(f)
    
    # Extract data for all particles
    particles = []
    for feature in geojson['features']:
        coordinates = feature['geometry']['coordinates']
        properties = feature['properties']
        
        lons = [coord[0] for coord in coordinates]
        lats = [coord[1] for coord in coordinates]
        
        particles.append({
            'name': properties['name'],
            'particle_id': properties['particle_id'],
            'lons': lons,
            'lats': lats,
            'plastic_density': properties.get('plastic_density', 920.0),
            'plastic_diameter': properties.get('plastic_diameter', 0.001),
            'plastic_amount': properties.get('plastic_amount', 1.0)
        })
    
    # Create visualization
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Colors for different particles
    colors = ['blue', 'red', 'green', 'orange']
    
    # Plot 1: All trajectories on map
    for i, particle in enumerate(particles):
        color = colors[i % len(colors)]
        
        # Plot trajectory
        ax1.plot(particle['lons'], particle['lats'], color=color, linewidth=2, 
                alpha=0.7, label=particle['name'])
        
        # Mark start and end
        ax1.plot(particle['lons'][0], particle['lats'][0], 'o', color=color, 
                markersize=8, markeredgecolor='black', markeredgewidth=1)
        ax1.plot(particle['lons'][-1], particle['lats'][-1], 's', color=color, 
                markersize=8, markeredgecolor='black', markeredgewidth=1)
        
        # Add hourly markers (every 6 hours)
        for j in range(0, len(particle['lons']), 6):
            ax1.plot(particle['lons'][j], particle['lats'][j], 'o', color=color, 
                    markersize=3, alpha=0.5)
    
    ax1.set_xlabel('Longitude (°W)')
    ax1.set_ylabel('Latitude (°N)')
    ax1.set_title('PlasticParcels Trajectories (36 hours)\nCircles=Start, Squares=End')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_aspect('equal')
    
    # Plot 2: Distance from initial position vs time
    for i, particle in enumerate(particles):
        color = colors[i % len(colors)]
        start_lon, start_lat = particle['lons'][0], particle['lats'][0]
        
        distances = []
        for lon, lat in zip(particle['lons'], particle['lats']):
            dlat = lat - start_lat
            dlon = (lon - start_lon) * np.cos(np.radians(start_lat))
            dist_km = np.sqrt(dlat**2 + dlon**2) * 111.32
            distances.append(dist_km)
        
        hours = list(range(len(distances)))
        ax2.plot(hours, distances, color=color, linewidth=2, label=particle['name'])
    
    ax2.set_xlabel('Time (hours)')
    ax2.set_ylabel('Distance from start (km)')
    ax2.set_title('Distance Traveled Over Time')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # Plot 3: Particle separation over time
    if len(particles) >= 2:
        # Calculate separation between particle 1 and others
        ref_particle = particles[0]
        
        for i in range(1, len(particles)):
            color = colors[i % len(colors)]
            separations = []
            
            for j in range(len(ref_particle['lons'])):
                dlat = particles[i]['lats'][j] - ref_particle['lats'][j]
                dlon = (particles[i]['lons'][j] - ref_particle['lons'][j]) * np.cos(np.radians(ref_particle['lats'][j]))
                sep_km = np.sqrt(dlat**2 + dlon**2) * 111.32
                separations.append(sep_km)
            
            hours = list(range(len(separations)))
            ax3.plot(hours, separations, color=color, linewidth=2, 
                    label=f'{ref_particle["name"]} - {particles[i]["name"]}')
    
    ax3.set_xlabel('Time (hours)')
    ax3.set_ylabel('Separation distance (km)')
    ax3.set_title('Particle Separation Over Time')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # Plot 4: Speed over time (estimated from position changes)
    for i, particle in enumerate(particles):
        color = colors[i % len(colors)]
        velocities = []
        
        for j in range(1, len(particle['lons'])):
            # Calculate velocity between consecutive points (1 hour apart)
            dlat = (particle['lats'][j] - particle['lats'][j-1]) * 111.32  # km
            dlon = (particle['lons'][j] - particle['lons'][j-1]) * 111.32 * np.cos(np.radians(particle['lats'][j]))  # km
            vel_kmh = np.sqrt(dlat**2 + dlon**2)  # km/h
            velocities.append(vel_kmh)
        
        hours = list(range(1, len(velocities) + 1))
        ax4.plot(hours, velocities, color=color, linewidth=1, alpha=0.7, label=particle['name'])
    
    ax4.set_xlabel('Time (hours)')
    ax4.set_ylabel('Speed (km/h)')
    ax4.set_title('Particle Speed Over Time')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('plasticparcels_trajectories.png', dpi=300, bbox_inches='tight')
    print("PlasticParcels plot saved as plasticparcels_trajectories.png")
    
    # Print summary statistics
    print(f"\n=== PlasticParcels Trajectory Summary ===")
    print(f"Number of particles: {len(particles)}")
    print(f"Duration: 36 hours")
    print(f"Model: PlasticParcels with NOAA NGOFS2 data")
    
    for i, particle in enumerate(particles):
        start_lat, start_lon = particle['lats'][0], particle['lons'][0]
        end_lat, end_lon = particle['lats'][-1], particle['lons'][-1]
        
        # Total distance
        total_distance = 0
        for j in range(1, len(particle['lons'])):
            dlat = (particle['lats'][j] - particle['lats'][j-1]) * 111.32
            dlon = (particle['lons'][j] - particle['lons'][j-1]) * 111.32 * np.cos(np.radians(particle['lats'][j]))
            total_distance += np.sqrt(dlat**2 + dlon**2)
        
        # Net displacement
        net_dlat = (end_lat - start_lat) * 111.32
        net_dlon = (end_lon - start_lon) * 111.32 * np.cos(np.radians(start_lat))
        net_displacement = np.sqrt(net_dlat**2 + net_dlon**2)
        
        print(f"\n{particle['name']}:")
        print(f"  Start: {start_lat:.6f}N, {abs(start_lon):.6f}W")
        print(f"  End: {end_lat:.6f}N, {abs(end_lon):.6f}W")
        print(f"  Total distance: {total_distance:.2f} km")
        print(f"  Net displacement: {net_displacement:.2f} km")
        print(f"  Average speed: {total_distance/36:.3f} km/h")
        print(f"  Plastic density: {particle['plastic_density']:.1f} kg/m³")
        print(f"  Plastic diameter: {particle['plastic_diameter']*1000:.1f} mm")
    
    # Calculate dispersion statistics
    if len(particles) > 1:
        print(f"\n=== Dispersion Analysis ===")
        
        # Initial separation
        initial_separations = []
        for i in range(len(particles)):
            for j in range(i+1, len(particles)):
                dlat = (particles[i]['lats'][0] - particles[j]['lats'][0]) * 111.32
                dlon = (particles[i]['lons'][0] - particles[j]['lons'][0]) * 111.32 * np.cos(np.radians(particles[i]['lats'][0]))
                sep = np.sqrt(dlat**2 + dlon**2)
                initial_separations.append(sep)
        
        # Final separation
        final_separations = []
        for i in range(len(particles)):
            for j in range(i+1, len(particles)):
                dlat = (particles[i]['lats'][-1] - particles[j]['lats'][-1]) * 111.32
                dlon = (particles[i]['lons'][-1] - particles[j]['lons'][-1]) * 111.32 * np.cos(np.radians(particles[i]['lats'][-1]))
                sep = np.sqrt(dlat**2 + dlon**2)
                final_separations.append(sep)
        
        print(f"Initial mean separation: {np.mean(initial_separations):.2f} km")
        print(f"Final mean separation: {np.mean(final_separations):.2f} km")
        print(f"Dispersion ratio: {np.mean(final_separations)/np.mean(initial_separations):.2f}")
    
    # Compare with simple advection
    print(f"\n=== Comparison with Simple Advection ===")
    print("PlasticParcels includes:")
    print("- Advanced particle tracking algorithms")
    print("- Proper interpolation methods")
    print("- Plastic-specific properties (density, size)")
    print("- Professional oceanographic modeling framework")

def main():
    """Main function"""
    print("=== PlasticParcels Trajectory Visualization ===")
    
    load_and_visualize_plasticparcels()
    
    print("\nVisualization complete!")

if __name__ == "__main__":
    main()
