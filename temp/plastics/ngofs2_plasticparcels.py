#!/usr/bin/env python3
"""
PlasticParcels simulation using NOAA NGOFS2 ocean current data
Adapted for unstructured grid data from SCHISM model
"""

import numpy as np
import xarray as xr
from datetime import datetime, timedelta
import json

# PlasticParcels and Parcels imports
import plasticparcels as pp
import parcels
from parcels import FieldSet, ParticleSet, JITParticle, Variable
from parcels import AdvectionRK4

# For unstructured grid interpolation
from scipy.spatial import cKDTree

class NGOFS2FieldSet:
    """Custom fieldset creator for NOAA NGOFS2 unstructured grid data"""
    
    def __init__(self, base_date="2025/06/12", base_hour=21, num_hours=42):
        self.base_date = base_date
        self.base_hour = base_hour
        self.num_hours = num_hours
        self.datasets = []
        self.times = []
        
    def download_ngofs2_data(self):
        """Download NGOFS2 data for the specified time range"""
        base_url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/"
        
        print("Downloading NGOFS2 data...")
        for hour_offset in range(0, min(self.num_hours, 42), 6):  # Every 6 hours
            forecast_hour = f"f{hour_offset:03d}"
            url = f"{base_url}{self.base_date}/ngofs2.t{self.base_hour:02d}z.20250612.fields.{forecast_hour}.nc"
            
            try:
                print(f"Loading forecast hour {hour_offset}...")
                ds = xr.open_dataset(url, engine='netcdf4')
                self.datasets.append(ds)
                
                # Calculate actual time for this forecast
                base_time = datetime(2025, 6, 12, self.base_hour)
                forecast_time = base_time + timedelta(hours=hour_offset)
                self.times.append(forecast_time)
                
            except Exception as e:
                print(f"Failed to load {url}: {e}")
                break
                
        print(f"Successfully loaded {len(self.datasets)} datasets")
        
    def create_structured_grid(self, lon_min=-97.2, lon_max=-96.2, lat_min=27.8, lat_max=28.8,
                              grid_res=0.01):
        """Create a structured grid from unstructured NGOFS2 data with proper land masking"""

        if not self.datasets:
            raise ValueError("No datasets loaded. Call download_ngofs2_data() first.")

        # Create regular grid - smaller domain focused on water
        lons = np.arange(lon_min, lon_max + grid_res, grid_res)
        lats = np.arange(lat_min, lat_max + grid_res, grid_res)
        depths = np.array([0.0, 10.0, 20.0])  # Simple depth levels

        # Convert times to seconds since first time
        time_seconds = np.array([(t - self.times[0]).total_seconds() for t in self.times])

        # Initialize velocity arrays
        U = np.zeros((len(time_seconds), len(depths), len(lats), len(lons)), dtype=np.float32)
        V = np.zeros((len(time_seconds), len(depths), len(lats), len(lons)), dtype=np.float32)
        W = np.zeros((len(time_seconds), len(depths), len(lats), len(lons)), dtype=np.float32)

        print("Interpolating unstructured data to regular grid with land masking...")

        # Get element centers and bathymetry from first dataset
        ds = self.datasets[0]
        lonc = ds['lonc'].values
        latc = ds['latc'].values

        # Get node coordinates and bathymetry for land masking
        lon_nodes = ds['lon'].values
        lat_nodes = ds['lat'].values
        h_nodes = ds['h'].values  # Depth at nodes

        # Build KDTree for element centers (velocities)
        points = np.column_stack([lonc, latc])
        tree = cKDTree(points)

        # Build KDTree for nodes (bathymetry)
        node_points = np.column_stack([lon_nodes, lat_nodes])
        node_tree = cKDTree(node_points)

        # Create meshgrid for interpolation targets
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        target_points = np.column_stack([lon_grid.ravel(), lat_grid.ravel()])

        # Find nearest neighbors for velocities and bathymetry
        _, vel_indices = tree.query(target_points, k=1)
        _, bathy_indices = node_tree.query(target_points, k=1)

        # Create land mask based on bathymetry
        bathy_interp = h_nodes[bathy_indices].reshape(lon_grid.shape)
        land_mask = bathy_interp <= 2.0  # Land or very shallow water (< 2m)

        print(f"Land mask: {np.sum(land_mask)} land points out of {land_mask.size} total points")

        # Interpolate for each time step
        for t_idx, ds in enumerate(self.datasets):
            print(f"Processing time step {t_idx+1}/{len(self.datasets)}")

            # Get surface velocities (first sigma layer)
            u_surf = ds['u'].values[0, 0, :]  # time=0, siglay=0 (surface)
            v_surf = ds['v'].values[0, 0, :]

            # Interpolate to regular grid
            u_interp = u_surf[vel_indices].reshape(lon_grid.shape)
            v_interp = v_surf[vel_indices].reshape(lon_grid.shape)

            # Apply land mask - set velocities to zero on land
            u_interp[land_mask] = 0.0
            v_interp[land_mask] = 0.0

            # Fill velocity arrays (surface and subsurface)
            U[t_idx, 0, :, :] = u_interp
            V[t_idx, 0, :, :] = v_interp

            # Simple vertical decay for subsurface layers
            for d_idx in range(1, len(depths)):
                decay_factor = (len(depths) - d_idx) / len(depths)
                U[t_idx, d_idx, :, :] = u_interp * decay_factor
                V[t_idx, d_idx, :, :] = v_interp * decay_factor

        # Create FieldSet
        data = {"U": U, "V": V, "W": W}
        dimensions = {"lon": lons, "lat": lats, "depth": depths, "time": time_seconds}

        fieldset = FieldSet.from_data(data, dimensions, mesh="spherical", allow_time_extrapolation=True)

        # Set interpolation methods
        fieldset.U.interp_method = "linear"
        fieldset.V.interp_method = "linear"
        fieldset.W.interp_method = "linear"

        # Add constants for PlasticParcels
        fieldset.add_constant('use_mixing', False)
        fieldset.add_constant('use_biofouling', False)
        fieldset.add_constant('use_stokes', False)
        fieldset.add_constant('use_wind', False)
        fieldset.add_constant('G', 9.81)
        fieldset.add_constant('use_3D', False)
        fieldset.add_constant('verbose_delete', True)

        # Add proper bathymetry with land masking
        bathymetry = bathy_interp.copy()
        bathymetry[land_mask] = 0.0  # Set land areas to zero depth
        bathymetry_data = {"bathymetry": bathymetry}
        bathymetry_dims = {"lon": lons, "lat": lats}
        bathymetry_fieldset = FieldSet.from_data(bathymetry_data, bathymetry_dims, mesh="spherical")
        fieldset.add_field(bathymetry_fieldset.bathymetry)

        return fieldset

def create_plastic_particles(fieldset, initial_positions, plastic_type="microplastic"):
    """Create plastic particles for the simulation"""
    
    # Extract positions
    lons = [pos[1] for pos in initial_positions]  # longitude
    lats = [pos[0] for pos in initial_positions]  # latitude
    
    # Plastic properties (microplastic defaults)
    plastic_properties = {
        "microplastic": {
            "density": 920.0,      # kg/m³ (slightly less than seawater)
            "diameter": 0.001,     # 1 mm
            "wind_coefficient": 0.01
        },
        "macroplastic": {
            "density": 950.0,      # kg/m³
            "diameter": 0.05,      # 5 cm
            "wind_coefficient": 0.03
        }
    }
    
    props = plastic_properties.get(plastic_type, plastic_properties["microplastic"])
    
    # Create particle class with plastic properties
    class PlasticParticle(JITParticle):
        plastic_diameter = Variable('plastic_diameter', dtype=np.float32, initial=props["diameter"])
        plastic_density = Variable('plastic_density', dtype=np.float32, initial=props["density"])
        wind_coefficient = Variable('wind_coefficient', dtype=np.float32, initial=props["wind_coefficient"])
        plastic_amount = Variable('plastic_amount', dtype=np.float32, initial=1.0)

    # Create particle set
    pset = ParticleSet.from_list(
        fieldset,
        PlasticParticle,
        lon=lons,
        lat=lats
    )
    
    return pset

def run_simulation(initial_positions, runtime_hours=36, output_file="ngofs2_plastic_trajectory.zarr"):
    """Run the plastic particle simulation"""

    print("=== NOAA NGOFS2 PlasticParcels Simulation ===")

    # Create fieldset
    ngofs2 = NGOFS2FieldSet()
    ngofs2.download_ngofs2_data()
    fieldset = ngofs2.create_structured_grid()

    print(f"Created fieldset with {len(fieldset.U.grid.time)} time steps")

    # Create particles
    pset = create_plastic_particles(fieldset, initial_positions)
    print(f"Created {len(pset)} plastic particles")

    # Define kernels - simple advection only (land masking handled in grid creation)
    kernels = [AdvectionRK4]

    # Output file
    output_file_obj = pset.ParticleFile(name=output_file, outputdt=timedelta(hours=1))

    # Run simulation
    print("Running simulation...")
    runtime = timedelta(hours=runtime_hours)
    dt = timedelta(minutes=30)  # 30-minute time step

    # Use scipy mode to avoid compilation issues
    pset.execute(
        kernels,
        runtime=runtime,
        dt=dt,
        output_file=output_file_obj,
        verbose_progress=True,
        mode='scipy'  # Use scipy instead of JIT compilation
    )

    print(f"Simulation complete. Output saved to {output_file}")
    return output_file

def convert_to_geojson(zarr_file, geojson_file="ngofs2_plastic_trajectory.geojson"):
    """Convert Parcels output to GeoJSON format"""
    
    print("Converting output to GeoJSON...")
    
    # Load the trajectory data
    ds = xr.open_zarr(zarr_file)
    
    features = []
    
    # Process each particle
    for p_id in ds.trajectory.values:
        particle_data = ds.sel(trajectory=p_id)
        
        # Extract valid (non-NaN) positions
        valid_mask = ~np.isnan(particle_data.lon.values)
        if not np.any(valid_mask):
            continue
            
        lons = particle_data.lon.values[valid_mask]
        lats = particle_data.lat.values[valid_mask]
        times = particle_data.time.values[valid_mask]
        
        # Create coordinates for LineString
        coordinates = [[float(lon), float(lat)] for lon, lat in zip(lons, lats)]
        
        # Create time series
        time_series = []
        for i, (lon, lat, time) in enumerate(zip(lons, lats, times)):
            time_str = np.datetime_as_string(time, unit='s')
            time_series.append({
                'time': time_str,
                'coordinates': [float(lon), float(lat)]
            })
        
        # Create feature
        feature = {
            'type': 'Feature',
            'geometry': {
                'type': 'LineString',
                'coordinates': coordinates
            },
            'properties': {
                'particle_id': int(p_id),
                'start_time': time_series[0]['time'] if time_series else None,
                'end_time': time_series[-1]['time'] if time_series else None,
                'duration_hours': len(time_series),
                'time_series': time_series
            }
        }
        
        features.append(feature)
    
    # Create GeoJSON
    geojson = {
        'type': 'FeatureCollection',
        'features': features
    }
    
    # Save to file
    with open(geojson_file, 'w') as f:
        json.dump(geojson, f, indent=2)
    
    print(f"GeoJSON saved to {geojson_file}")
    return geojson

def main():
    """Main function to run the simulation"""

    # Define initial positions (4 drifters) - moved further offshore to avoid land
    # Original position was too close to shore, move south into deeper water
    center_lat = 28.2  # Moved south from 28.3785805
    center_lon = -96.8  # Moved west from -96.7531298 (further offshore)

    # Generate nearby positions in deeper water
    np.random.seed(42)
    initial_positions = [[center_lat, center_lon]]  # Main position

    for i in range(3):  # Add 3 more nearby positions
        dlat = np.random.uniform(-0.05, 0.05)  # ±0.05 degrees (~5 km)
        dlon = np.random.uniform(-0.05, 0.05)
        # Ensure all positions are offshore (south and west of original)
        new_lat = center_lat + dlat
        new_lon = center_lon + dlon
        initial_positions.append([new_lat, new_lon])

    print("Initial positions (moved offshore to avoid land):")
    for i, pos in enumerate(initial_positions):
        print(f"  Particle {i+1}: {pos[0]:.6f}N, {abs(pos[1]):.6f}W")

    # Run simulation
    zarr_output = run_simulation(initial_positions, runtime_hours=36)

    # Convert to GeoJSON
    geojson_output = convert_to_geojson(zarr_output)

    print("\n=== Simulation Summary ===")
    print(f"Particles: {len(initial_positions)}")
    print(f"Duration: 36 hours")
    print(f"Zarr output: {zarr_output}")
    print(f"GeoJSON output: ngofs2_plastic_trajectory.geojson")

    return geojson_output

if __name__ == "__main__":
    main()
