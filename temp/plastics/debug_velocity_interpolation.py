#!/usr/bin/env python3
"""
Debug velocity interpolation to understand why particles are stationary
"""

import numpy as np
import xarray as xr
from scipy.spatial import cKDTree
from scipy.interpolate import RegularGridInterpolator

def debug_velocity_interpolation():
    """Debug the velocity interpolation process"""
    
    # Load one dataset
    url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/2025/06/12/ngofs2.t21z.20250612.fields.f000.nc"
    ds = xr.open_dataset(url, engine='netcdf4')
    
    # Get coordinates and convert longitude
    lonc = ds['lonc'].values
    latc = ds['latc'].values
    lonc = np.where(lonc > 180, lonc - 360, lonc)
    
    # Define our region
    lon_min, lon_max = -97.2, -96.2
    lat_min, lat_max = 27.8, 28.8
    
    # Filter to region
    region_mask = ((lonc >= lon_min - 0.5) & (lonc <= lon_max + 0.5) & 
                   (latc >= lat_min - 0.5) & (latc <= lat_max + 0.5))
    
    print(f"Elements in region: {np.sum(region_mask)}")
    
    # Get regional data
    lonc_region = lonc[region_mask]
    latc_region = latc[region_mask]
    
    # Get velocities
    u_surf_all = ds['u'].values[0, 0, :]
    v_surf_all = ds['v'].values[0, 0, :]
    u_surf = u_surf_all[region_mask]
    v_surf = v_surf_all[region_mask]
    
    print(f"Regional velocity ranges:")
    print(f"U: [{np.nanmin(u_surf):.4f}, {np.nanmax(u_surf):.4f}] m/s")
    print(f"V: [{np.nanmin(v_surf):.4f}, {np.nanmax(v_surf):.4f}] m/s")
    print(f"Valid U points: {np.sum(~np.isnan(u_surf))}/{len(u_surf)}")
    print(f"Valid V points: {np.sum(~np.isnan(v_surf))}/{len(v_surf)}")
    
    # Test interpolation at our NEW particle positions (moved to deeper water)
    particle_positions = [
        (28.0, -96.9),
        (27.95, -96.95),
        (28.05, -96.85),
        (27.98, -96.92)
    ]
    
    # Build KDTree
    vel_tree = cKDTree(np.column_stack([lonc_region, latc_region]))
    
    print(f"\n=== Testing Interpolation at Particle Positions ===")
    
    for i, (lat, lon) in enumerate(particle_positions):
        print(f"\nParticle {i+1} at ({lat:.3f}°N, {lon:.3f}°W):")
        
        # Find nearest neighbor
        dist, idx = vel_tree.query([lon, lat], k=1)
        
        print(f"  Nearest element distance: {dist*111:.2f} km")
        print(f"  Nearest element coords: ({latc_region[idx]:.4f}°N, {lonc_region[idx]:.4f}°W)")
        print(f"  Nearest element U: {u_surf[idx]:.6f} m/s")
        print(f"  Nearest element V: {v_surf[idx]:.6f} m/s")
        
        if np.isnan(u_surf[idx]) or np.isnan(v_surf[idx]):
            print(f"  ⚠️  WARNING: NaN velocity at nearest element!")
        
        if abs(u_surf[idx]) < 1e-10 and abs(v_surf[idx]) < 1e-10:
            print(f"  ⚠️  WARNING: Zero velocity at nearest element!")
    
    # Test grid interpolation
    print(f"\n=== Testing Grid Interpolation ===")
    
    # Create small test grid
    lons = np.arange(-96.9, -96.7, 0.05)
    lats = np.arange(28.1, 28.3, 0.05)
    
    lon_grid, lat_grid = np.meshgrid(lons, lats)
    target_points = np.column_stack([lon_grid.ravel(), lat_grid.ravel()])
    
    # Interpolate
    _, vel_indices = vel_tree.query(target_points, k=1)
    u_interp = u_surf[vel_indices].reshape(lon_grid.shape)
    v_interp = v_surf[vel_indices].reshape(lon_grid.shape)
    
    print(f"Grid interpolation results:")
    print(f"U grid range: [{np.nanmin(u_interp):.6f}, {np.nanmax(u_interp):.6f}]")
    print(f"V grid range: [{np.nanmin(v_interp):.6f}, {np.nanmax(v_interp):.6f}]")
    print(f"Non-zero U points: {np.sum(np.abs(u_interp) > 1e-10)}/{u_interp.size}")
    print(f"Non-zero V points: {np.sum(np.abs(v_interp) > 1e-10)}/{v_interp.size}")
    
    # Check if all velocities are the same (sign of bad interpolation)
    unique_u = np.unique(u_interp[~np.isnan(u_interp)])
    unique_v = np.unique(v_interp[~np.isnan(v_interp)])
    
    print(f"Unique U values: {len(unique_u)}")
    print(f"Unique V values: {len(unique_v)}")
    
    if len(unique_u) == 1:
        print(f"⚠️  All U values are the same: {unique_u[0]}")
    if len(unique_v) == 1:
        print(f"⚠️  All V values are the same: {unique_v[0]}")
    
    # Test RegularGridInterpolator approach
    print(f"\n=== Testing RegularGridInterpolator ===")
    
    # Create time array
    time_seconds = np.array([0.0])
    
    # Create 3D arrays for interpolator
    U_3d = u_interp[np.newaxis, :, :]  # Add time dimension
    V_3d = v_interp[np.newaxis, :, :]
    
    try:
        u_interpolator = RegularGridInterpolator((time_seconds, lats, lons), U_3d, 
                                                bounds_error=False, fill_value=0.0)
        v_interpolator = RegularGridInterpolator((time_seconds, lats, lons), V_3d, 
                                                bounds_error=False, fill_value=0.0)
        
        # Test interpolation at particle positions
        for i, (lat, lon) in enumerate(particle_positions):
            u_val = u_interpolator([0.0, lat, lon])[0]
            v_val = v_interpolator([0.0, lat, lon])[0]
            print(f"Particle {i+1}: U={u_val:.6f}, V={v_val:.6f}")
            
    except Exception as e:
        print(f"RegularGridInterpolator error: {e}")
    
    # Check land masking
    print(f"\n=== Checking Land Masking ===")
    
    # Get bathymetry
    lon_nodes = ds['lon'].values
    lat_nodes = ds['lat'].values
    h_nodes = ds['h'].values
    lon_nodes = np.where(lon_nodes > 180, lon_nodes - 360, lon_nodes)
    
    bathy_tree = cKDTree(np.column_stack([lon_nodes, lat_nodes]))
    
    for i, (lat, lon) in enumerate(particle_positions):
        _, bathy_idx = bathy_tree.query([lon, lat], k=1)
        depth = h_nodes[bathy_idx]
        print(f"Particle {i+1} depth: {depth:.2f} m")
        
        if depth <= 2.0:
            print(f"  ⚠️  Particle {i+1} is in shallow water/land!")
    
    ds.close()

if __name__ == "__main__":
    debug_velocity_interpolation()
