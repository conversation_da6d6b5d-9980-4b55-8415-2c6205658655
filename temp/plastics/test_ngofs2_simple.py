#!/usr/bin/env python3
"""
Simple test to debug NGOFS2 data access and basic functionality
"""

import numpy as np
import xarray as xr
from datetime import datetime, timedelta
import json

def test_ngofs2_access():
    """Test basic NGOFS2 data access"""
    print("Testing NGOFS2 data access...")
    
    url = "https://opendap.co-ops.nos.noaa.gov/thredds/dodsC/NOAA/NGOFS2/MODELS/2025/06/12/ngofs2.t21z.20250612.fields.f000.nc"
    
    try:
        print(f"Attempting to open: {url}")
        ds = xr.open_dataset(url, engine='netcdf4')
        print("✓ Successfully opened NGOFS2 dataset")
        
        # Check basic info
        print(f"Variables: {list(ds.variables.keys())}")
        print(f"Dimensions: {dict(ds.dims)}")
        
        # Check coordinates
        if 'lonc' in ds.variables:
            lonc = ds['lonc'].values
            latc = ds['latc'].values
            print(f"Element centers: {len(lonc)} points")
            print(f"Longitude range: {lonc.min():.3f} to {lonc.max():.3f}")
            print(f"Latitude range: {latc.min():.3f} to {latc.max():.3f}")
        
        # Check velocities
        if 'u' in ds.variables and 'v' in ds.variables:
            u = ds['u'].values[0, 0, :]  # First time, surface layer
            v = ds['v'].values[0, 0, :]
            print(f"U velocity range: {np.nanmin(u):.4f} to {np.nanmax(u):.4f}")
            print(f"V velocity range: {np.nanmin(v):.4f} to {np.nanmax(v):.4f}")
            print(f"Valid velocity points: {np.sum(~np.isnan(u))}/{len(u)}")
        
        # Check bathymetry
        if 'h' in ds.variables:
            h = ds['h'].values
            print(f"Depth range: {np.nanmin(h):.2f} to {np.nanmax(h):.2f} m")
            land_points = np.sum(h <= 0)
            shallow_points = np.sum((h > 0) & (h < 2))
            deep_points = np.sum(h >= 2)
            print(f"Land points (h <= 0): {land_points}")
            print(f"Shallow points (0 < h < 2): {shallow_points}")
            print(f"Deep points (h >= 2): {deep_points}")
        
        ds.close()
        return True
        
    except Exception as e:
        print(f"✗ Error accessing NGOFS2 data: {e}")
        return False

def test_initial_positions():
    """Test if initial positions are reasonable"""
    print("\nTesting initial positions...")
    
    # Original problematic position
    orig_lat = 28.3785805
    orig_lon = -96.7531298
    
    # New offshore position
    new_lat = 28.2
    new_lon = -96.8
    
    print(f"Original position: {orig_lat:.6f}N, {abs(orig_lon):.6f}W")
    print(f"New position: {new_lat:.6f}N, {abs(new_lon):.6f}W")
    print(f"Moved {(orig_lat - new_lat)*111:.1f} km south")
    print(f"Moved {(orig_lon - new_lon)*111:.1f} km west")

def test_parcels_import():
    """Test if parcels can be imported and basic functionality works"""
    print("\nTesting Parcels import...")
    
    try:
        import parcels
        print(f"✓ Parcels version: {parcels.__version__}")
        
        from parcels import FieldSet, ParticleSet, JITParticle, AdvectionRK4
        print("✓ Successfully imported Parcels components")
        
        # Test basic fieldset creation
        lons = np.arange(-97, -96, 0.1)
        lats = np.arange(28, 29, 0.1)
        times = np.array([0, 3600])  # 0 and 1 hour
        
        U = np.random.random((len(times), 1, len(lats), len(lons))) * 0.1
        V = np.random.random((len(times), 1, len(lats), len(lons))) * 0.1
        
        data = {"U": U, "V": V}
        dimensions = {"lon": lons, "lat": lats, "depth": [0], "time": times}
        
        fieldset = FieldSet.from_data(data, dimensions, mesh="spherical")
        print("✓ Successfully created test fieldset")
        
        # Test particle creation
        pset = ParticleSet.from_list(fieldset, JITParticle, lon=[-96.5], lat=[28.5])
        print("✓ Successfully created test particle set")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with Parcels: {e}")
        return False

def main():
    """Run all tests"""
    print("=== NGOFS2 Debugging Tests ===\n")
    
    # Test 1: Data access
    data_ok = test_ngofs2_access()
    
    # Test 2: Position check
    test_initial_positions()
    
    # Test 3: Parcels functionality
    parcels_ok = test_parcels_import()
    
    print(f"\n=== Test Results ===")
    print(f"NGOFS2 data access: {'✓ PASS' if data_ok else '✗ FAIL'}")
    print(f"Parcels functionality: {'✓ PASS' if parcels_ok else '✗ FAIL'}")
    
    if data_ok and parcels_ok:
        print("\n✓ All tests passed! Ready to run simulation.")
    else:
        print("\n✗ Some tests failed. Check errors above.")

if __name__ == "__main__":
    main()
